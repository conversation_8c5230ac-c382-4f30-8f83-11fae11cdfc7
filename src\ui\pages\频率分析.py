import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import streamlit as st
from plotly.subplots import make_subplots


def show_frequency_analysis():
    """频率分析页面 - 基于真实历史数据"""
    st.header("🔢 频率分析")

    # 添加交互控制面板
    st.subheader("🎛️ 分析控制面板")

    # 创建控制面板布局
    control_col1, control_col2, control_col3, control_col4 = st.columns(4)

    with control_col1:
        # 位置选择器
        position_filter = st.selectbox(
            "📍 选择分析位置",
            options=["all", "hundreds", "tens", "units"],
            format_func=lambda x: {
                "all": "全部位置",
                "hundreds": "仅百位",
                "tens": "仅十位",
                "units": "仅个位"
            }[x],
            index=0
        )

    with control_col2:
        # 数字筛选器
        digit_filter = st.multiselect(
            "🔢 筛选数字",
            options=list(range(10)),
            default=list(range(10)),
            format_func=lambda x: f"数字{x}"
        )

    with control_col3:
        # 显示模式选择
        display_mode = st.selectbox(
            "📊 显示模式",
            options=["complete", "heatmap_only", "stats_only"],
            format_func=lambda x: {
                "complete": "完整分析",
                "heatmap_only": "仅热力图",
                "stats_only": "仅统计表"
            }[x],
            index=0
        )

    with control_col4:
        # 缓存控制
        use_cache = st.checkbox("🚀 使用缓存", value=True, help="启用缓存可提升加载速度")

    # 高级筛选选项（可折叠）
    with st.expander("🔧 高级筛选选项"):
        advanced_col1, advanced_col2 = st.columns(2)

        with advanced_col1:
            # 频率范围筛选
            freq_range = st.slider(
                "频率范围筛选",
                min_value=0,
                max_value=1000,
                value=(0, 1000),
                help="筛选指定频率范围内的数字"
            )

        with advanced_col2:
            # 排序方式
            sort_method = st.selectbox(
                "排序方式",
                options=["frequency_desc", "frequency_asc", "digit_asc", "digit_desc"],
                format_func=lambda x: {
                    "frequency_desc": "频率降序",
                    "frequency_asc": "频率升序",
                    "digit_asc": "数字升序",
                    "digit_desc": "数字降序"
                }[x],
                index=0
            )

    # 构建API URL
    api_url = f"http://127.0.0.1:8888/api/v1/analysis/frequency?position={position_filter}&use_cache={str(use_cache).lower()}"

    # 性能优化：缓存键生成
    cache_key = f"freq_analysis_{position_filter}_{len(digit_filter)}_{freq_range}_{sort_method}_{display_mode}"

    # 获取真实的频率分析数据
    try:
        # 性能指示器
        start_time = st.empty()
        progress_bar = st.progress(0)
        status_text = st.empty()

        start_time.write("⏱️ 开始数据分析...")
        progress_bar.progress(10)
        status_text.write("正在连接API服务...")

        with st.spinner("正在分析频率数据..."):
            response = requests.get(api_url, timeout=15)
            progress_bar.progress(50)
            status_text.write("正在处理数据...")

            response.raise_for_status()
            frequency_data = response.json()

            progress_bar.progress(80)
            status_text.write("正在渲染界面...")

        # 显示性能信息
        query_time = frequency_data.get('query_time_ms', 0)
        cached = frequency_data.get('cached', False)

        progress_bar.progress(100)
        status_text.write(f"✅ 完成！查询耗时: {query_time:.2f}ms {'(缓存)' if cached else '(实时)'}")

        # 清理性能指示器
        import time
        time.sleep(1)
        start_time.empty()
        progress_bar.empty()
        status_text.empty()

        if frequency_data.get('success'):
            # 显示数据概览
            st.subheader("📊 数据概览")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_records = frequency_data.get('total_records', 0)
                st.metric("总记录数", f"{total_records:,}")
            with col2:
                date_range = frequency_data.get('date_range', 'N/A')
                st.metric("分析期间", date_range)
            with col3:
                latest_period = frequency_data.get('latest_period', 'N/A')
                st.metric("最新期号", latest_period)
            with col4:
                # 显示性能指标
                query_time_display = f"{query_time:.2f}ms"
                if cached:
                    query_time_display += " 🚀"
                st.metric("查询耗时", query_time_display, help="🚀 表示使用了缓存")

            # 性能优化建议
            if query_time > 1000:  # 超过1秒
                st.warning("⚠️ 查询耗时较长，建议启用缓存或减少筛选条件")
            elif query_time > 500:  # 超过0.5秒
                st.info("💡 提示：启用缓存可以显著提升加载速度")

            # 显示筛选状态
            if len(digit_filter) < 10 or position_filter != "all" or freq_range != (0, 1000):
                filter_info = []
                if position_filter != "all":
                    filter_info.append(f"位置: {position_filter}")
                if len(digit_filter) < 10:
                    filter_info.append(f"数字: {len(digit_filter)}/10")
                if freq_range != (0, 1000):
                    filter_info.append(f"频率: {freq_range[0]}-{freq_range[1]}")

                st.info(f"🔍 当前筛选: {' | '.join(filter_info)}")
            
            # 数字频率统计
            st.subheader("🔢 各数字出现频率")
            
            digit_freq = frequency_data.get('digit_frequency', {})
            
            if digit_freq:
                # 创建频率分布图
                digits = list(range(10))
                frequencies = [digit_freq.get(str(i), 0) for i in digits]
                
                # 计算期望频率（理论上每个数字应该出现的次数）
                expected_freq = total_records * 3 / 10  # 每期3个数字，10个可能的数字
                
                fig_bar = px.bar(
                    x=digits,
                    y=frequencies,
                    title=f"各数字出现频率分布（基于{total_records:,}条真实历史数据）",
                    labels={'x': '数字', 'y': '出现次数'},
                    color=frequencies,
                    color_continuous_scale='viridis'
                )
                
                # 添加期望频率线
                fig_bar.add_hline(
                    y=expected_freq, 
                    line_dash="dash", 
                    line_color="red",
                    annotation_text=f"期望频率: {expected_freq:.0f}"
                )
                
                fig_bar.update_layout(showlegend=False, height=500)
                st.plotly_chart(fig_bar, use_container_width=True)
                
                # 频率统计表
                freq_df = pd.DataFrame({
                    '数字': digits,
                    '出现次数': frequencies,
                    '出现频率': [f"{(freq/total_records/3*100):.2f}%" for freq in frequencies],
                    '期望次数': [f"{expected_freq:.0f}"] * 10,
                    '偏差': [f"{freq - expected_freq:+.0f}" for freq in frequencies]
                })
                
                st.dataframe(
                    freq_df, 
                    hide_index=True, 
                    use_container_width=True,
                    column_config={
                        "数字": st.column_config.NumberColumn("数字", width="small"),
                        "出现次数": st.column_config.NumberColumn("出现次数", width="medium"),
                        "出现频率": st.column_config.TextColumn("出现频率", width="medium"),
                        "期望次数": st.column_config.TextColumn("期望次数", width="medium"),
                        "偏差": st.column_config.TextColumn("偏差", width="medium")
                    }
                )
            
            # 热号冷号分析
            st.subheader("🔥❄️ 热号冷号分析")
            
            hot_numbers = frequency_data.get('hot_numbers', [])
            cold_numbers = frequency_data.get('cold_numbers', [])
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**🔥 热号 (出现频率最高)**")
                if hot_numbers:
                    for i, (num, freq) in enumerate(hot_numbers[:5], 1):
                        percentage = (freq / total_records / 3) * 100
                        st.write(f"{i}. 数字 **{num}**: {freq} 次 ({percentage:.2f}%)")
                else:
                    st.info("暂无热号数据")
            
            with col2:
                st.write("**❄️ 冷号 (出现频率最低)**")
                if cold_numbers:
                    for i, (num, freq) in enumerate(cold_numbers[:5], 1):
                        percentage = (freq / total_records / 3) * 100
                        st.write(f"{i}. 数字 **{num}**: {freq} 次 ({percentage:.2f}%)")
                else:
                    st.info("暂无冷号数据")
            
            # 位置频率分析
            if 'position_frequency' in frequency_data:
                st.subheader("📍 位置频率分析")

                position_freq_list = frequency_data['position_frequency']

                # 数据验证和错误处理
                if not isinstance(position_freq_list, list):
                    st.error("位置频率数据格式错误：期望列表格式")
                elif len(position_freq_list) == 0:
                    st.info("暂无位置频率数据")
                else:
                    try:
                        # 将列表格式转换为字典格式以便处理
                        position_dict = {}
                        for item in position_freq_list:
                            if isinstance(item, dict) and all(key in item for key in ['位置', '数字', '频率']):
                                pos = item['位置']
                                digit = item['数字']
                                freq = item['频率']

                                if pos not in position_dict:
                                    position_dict[pos] = {}
                                position_dict[pos][digit] = freq

                        # 应用筛选条件创建位置频率热力图数据
                        position_data = []
                        positions = ['百位', '十位', '个位']

                        # 根据位置筛选调整显示的位置
                        if position_filter != "all":
                            position_map = {
                                "hundreds": ["百位"],
                                "tens": ["十位"],
                                "units": ["个位"]
                            }
                            positions = position_map.get(position_filter, positions)

                        for pos in positions:
                            if pos in position_dict:
                                for digit in range(10):
                                    # 应用数字筛选
                                    if digit not in digit_filter:
                                        continue

                                    digit_str = str(digit)
                                    freq = position_dict[pos].get(digit_str, 0)

                                    # 应用频率范围筛选
                                    if not (freq_range[0] <= freq <= freq_range[1]):
                                        continue

                                    position_data.append({
                                        '位置': pos,
                                        '数字': digit_str,
                                        '频率': freq
                                    })

                        if position_data:
                            # 应用排序
                            if sort_method == "frequency_desc":
                                position_data.sort(key=lambda x: x['频率'], reverse=True)
                            elif sort_method == "frequency_asc":
                                position_data.sort(key=lambda x: x['频率'])
                            elif sort_method == "digit_asc":
                                position_data.sort(key=lambda x: int(x['数字']))
                            elif sort_method == "digit_desc":
                                position_data.sort(key=lambda x: int(x['数字']), reverse=True)

                            # 创建数据框和透视表
                            position_df = pd.DataFrame(position_data)

                            # 根据显示模式决定显示内容
                            if display_mode in ["complete", "heatmap_only"]:
                                pivot_df = position_df.pivot(index='数字', columns='位置', values='频率')

                                # 确保列顺序正确（只显示筛选后的位置）
                                available_positions = [pos for pos in ['百位', '十位', '个位'] if pos in pivot_df.columns]
                                pivot_df = pivot_df.reindex(columns=available_positions)

                                # 热力图
                                fig_heatmap = px.imshow(
                                    pivot_df.T,
                                    title=f"各位置数字出现频率热力图 (筛选: {len(digit_filter)}个数字)",
                                    labels={'x': '数字', 'y': '位置', 'color': '出现次数'},
                                    color_continuous_scale='RdYlBu_r',
                                    aspect="auto"
                                )
                                fig_heatmap.update_layout(height=400)
                                st.plotly_chart(fig_heatmap, use_container_width=True)

                            if display_mode in ["complete", "stats_only"]:
                                # 位置频率统计表
                                st.write("**位置频率详细统计**")
                                if 'pivot_df' in locals():
                                    st.dataframe(pivot_df, use_container_width=True)
                                else:
                                    # 如果只显示统计表，重新创建pivot_df
                                    pivot_df = position_df.pivot(index='数字', columns='位置', values='频率')
                                    available_positions = [pos for pos in ['百位', '十位', '个位'] if pos in pivot_df.columns]
                                    pivot_df = pivot_df.reindex(columns=available_positions)
                                    st.dataframe(pivot_df, use_container_width=True)

                            # 添加分位置详细统计
                            st.subheader("📊 分位置详细统计")

                            cols = st.columns(3)
                            for i, pos in enumerate(['百位', '十位', '个位']):
                                with cols[i]:
                                    st.write(f"**{pos}统计**")
                                    if pos in position_dict:
                                        pos_data = position_dict[pos]
                                        total_count = sum(pos_data.values())

                                        # 计算百分比并排序
                                        pos_stats = []
                                        for digit, count in pos_data.items():
                                            percentage = (count / total_count * 100) if total_count > 0 else 0
                                            pos_stats.append({
                                                '数字': digit,
                                                '次数': count,
                                                '百分比': f"{percentage:.2f}%"
                                            })

                                        # 按次数排序
                                        pos_stats.sort(key=lambda x: x['次数'], reverse=True)

                                        # 显示前5名
                                        for j, stat in enumerate(pos_stats[:5]):
                                            st.write(f"{j+1}. 数字{stat['数字']}: {stat['次数']}次 ({stat['百分比']})")
                                    else:
                                        st.info("暂无数据")

                            # 添加位置对比分析
                            st.subheader("🔄 位置对比分析")

                            # 创建位置对比柱状图
                            comparison_data = []
                            for digit in range(10):
                                digit_str = str(digit)
                                for pos in ['百位', '十位', '个位']:
                                    if pos in position_dict:
                                        freq = position_dict[pos].get(digit_str, 0)
                                        comparison_data.append({
                                            '数字': digit_str,
                                            '位置': pos,
                                            '频率': freq
                                        })

                            if comparison_data:
                                comparison_df = pd.DataFrame(comparison_data)

                                # 位置对比柱状图
                                fig_comparison = px.bar(
                                    comparison_df,
                                    x='数字',
                                    y='频率',
                                    color='位置',
                                    title="各位置数字频率对比",
                                    barmode='group',
                                    color_discrete_map={
                                        '百位': '#FF6B6B',
                                        '十位': '#4ECDC4',
                                        '个位': '#45B7D1'
                                    }
                                )
                                fig_comparison.update_layout(height=400)
                                st.plotly_chart(fig_comparison, use_container_width=True)

                                # 位置偏好度分析
                                st.write("**📊 位置偏好度分析**")

                                preference_data = []
                                for digit in range(10):
                                    digit_str = str(digit)
                                    freqs = []
                                    for pos in ['百位', '十位', '个位']:
                                        if pos in position_dict:
                                            freqs.append(position_dict[pos].get(digit_str, 0))
                                        else:
                                            freqs.append(0)

                                    if sum(freqs) > 0:
                                        # 计算偏好度（标准差/平均值）
                                        mean_freq = sum(freqs) / len(freqs)
                                        variance = sum((f - mean_freq) ** 2 for f in freqs) / len(freqs)
                                        std_dev = variance ** 0.5
                                        preference_score = (std_dev / mean_freq) if mean_freq > 0 else 0

                                        # 找出最偏好的位置
                                        max_freq = max(freqs)
                                        preferred_pos = ['百位', '十位', '个位'][freqs.index(max_freq)]

                                        preference_data.append({
                                            '数字': digit_str,
                                            '偏好位置': preferred_pos,
                                            '偏好度': f"{preference_score:.3f}",
                                            '百位': freqs[0],
                                            '十位': freqs[1],
                                            '个位': freqs[2]
                                        })

                                if preference_data:
                                    preference_df = pd.DataFrame(preference_data)
                                    st.dataframe(preference_df, use_container_width=True)

                                    # 位置相关性分析
                                    st.write("**🔗 位置相关性分析**")

                                    # 创建相关性矩阵
                                    corr_data = {}
                                    for pos in ['百位', '十位', '个位']:
                                        if pos in position_dict:
                                            corr_data[pos] = [position_dict[pos].get(str(d), 0) for d in range(10)]

                                    if len(corr_data) >= 2:
                                        corr_df = pd.DataFrame(corr_data)
                                        correlation_matrix = corr_df.corr()

                                        # 相关性热力图
                                        fig_corr = px.imshow(
                                            correlation_matrix,
                                            title="位置间数字频率相关性",
                                            color_continuous_scale='RdBu',
                                            aspect="auto"
                                        )
                                        fig_corr.update_layout(height=300)
                                        st.plotly_chart(fig_corr, use_container_width=True)

                                        # 显示相关性数值
                                        st.write("**相关性系数：**")
                                        for i, pos1 in enumerate(['百位', '十位', '个位']):
                                            for j, pos2 in enumerate(['百位', '十位', '个位']):
                                                if i < j and pos1 in correlation_matrix.columns and pos2 in correlation_matrix.columns:
                                                    corr_value = correlation_matrix.loc[pos1, pos2]
                                                    st.write(f"- {pos1} vs {pos2}: {corr_value:.3f}")
                        else:
                            st.warning("无法生成位置频率图表：数据为空")

                    except Exception as e:
                        st.error(f"处理位置频率数据时出错：{str(e)}")
                        st.info("请检查数据格式或联系技术支持")
            
            # 频率趋势分析
            if 'frequency_trend' in frequency_data:
                st.subheader("📈 频率趋势分析")
                
                trend_data = frequency_data['frequency_trend']
                
                # 选择要分析的数字
                selected_digits = st.multiselect(
                    "选择要分析趋势的数字",
                    options=list(range(10)),
                    default=[0, 1, 2, 3, 4],
                    max_selections=5,
                    help="最多可选择5个数字进行趋势分析"
                )
                
                if selected_digits and trend_data:
                    fig_trend = go.Figure()
                    
                    for digit in selected_digits:
                        if str(digit) in trend_data:
                            periods = trend_data[str(digit)].get('periods', [])
                            frequencies = trend_data[str(digit)].get('frequencies', [])
                            
                            if periods and frequencies:
                                fig_trend.add_trace(go.Scatter(
                                    x=periods,
                                    y=frequencies,
                                    mode='lines+markers',
                                    name=f'数字 {digit}',
                                    line=dict(width=2),
                                    marker=dict(size=4)
                                ))
                    
                    fig_trend.update_layout(
                        title="数字出现频率趋势（滑动窗口统计）",
                        xaxis_title="期号",
                        yaxis_title="累计频率",
                        hovermode='x unified',
                        height=500
                    )
                    
                    st.plotly_chart(fig_trend, use_container_width=True)
            
            # 连号分析
            if 'consecutive_analysis' in frequency_data:
                st.subheader("🔗 连号分析")
                
                consecutive_data = frequency_data['consecutive_analysis']
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**连续出现统计**")
                    consecutive_stats = consecutive_data.get('consecutive_stats', {})
                    for digit, stats in consecutive_stats.items():
                        max_consecutive = stats.get('max_consecutive', 0)
                        avg_consecutive = stats.get('avg_consecutive', 0)
                        st.write(f"数字 {digit}: 最长连续 {max_consecutive} 期, 平均 {avg_consecutive:.1f} 期")
                
                with col2:
                    st.write("**间隔出现统计**")
                    interval_stats = consecutive_data.get('interval_stats', {})
                    for digit, stats in interval_stats.items():
                        max_interval = stats.get('max_interval', 0)
                        avg_interval = stats.get('avg_interval', 0)
                        st.write(f"数字 {digit}: 最长间隔 {max_interval} 期, 平均 {avg_interval:.1f} 期")
            
            # 统计学分析
            if 'statistical_analysis' in frequency_data:
                st.subheader("📊 统计学分析")
                
                stats = frequency_data['statistical_analysis']
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    chi_square = stats.get('chi_square_statistic', 0)
                    st.metric("卡方检验统计量", f"{chi_square:.4f}")
                with col2:
                    p_value = stats.get('p_value', 0)
                    st.metric("P值", f"{p_value:.6f}")
                with col3:
                    degrees_freedom = stats.get('degrees_of_freedom', 9)
                    st.metric("自由度", degrees_freedom)
                with col4:
                    significance = "显著" if p_value < 0.05 else "不显著"
                    st.metric("显著性(α=0.05)", significance)
                
                # 统计学解释
                st.write("**统计学解释:**")
                if p_value < 0.05:
                    st.warning("⚠️ 频率分布存在显著性差异，可能存在非随机模式")
                    st.write("- 某些数字的出现频率明显偏离期望值")
                    st.write("- 建议进一步分析具体的偏差模式")
                else:
                    st.success("✅ 频率分布符合随机性假设")
                    st.write("- 各数字的出现频率接近期望值")
                    st.write("- 数据符合随机分布的统计特征")
                
                # 置信区间
                if 'confidence_intervals' in stats:
                    st.write("**95%置信区间:**")
                    ci_data = stats['confidence_intervals']
                    ci_df = pd.DataFrame([
                        {
                            '数字': digit,
                            '下限': f"{ci['lower']:.0f}",
                            '上限': f"{ci['upper']:.0f}",
                            '实际值': frequencies[int(digit)],
                            '在区间内': '✅' if ci['lower'] <= frequencies[int(digit)] <= ci['upper'] else '❌'
                        }
                        for digit, ci in ci_data.items()
                    ])
                    st.dataframe(ci_df, hide_index=True, use_container_width=True)
            
            # 预测建议
            if 'prediction_suggestions' in frequency_data:
                st.subheader("💡 基于频率的预测建议")
                
                suggestions = frequency_data['prediction_suggestions']
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**推荐关注数字:**")
                    recommended = suggestions.get('recommended_numbers', [])
                    for num in recommended:
                        st.write(f"- 数字 {num}")
                
                with col2:
                    st.write("**建议回避数字:**")
                    avoid = suggestions.get('avoid_numbers', [])
                    for num in avoid:
                        st.write(f"- 数字 {num}")
                
                if 'reasoning' in suggestions:
                    st.write("**分析依据:**")
                    for reason in suggestions['reasoning']:
                        st.write(f"- {reason}")
            
        else:
            st.error("频率分析API返回错误")
            if 'message' in frequency_data:
                st.error(f"错误信息: {frequency_data['message']}")
            
    except requests.exceptions.RequestException as e:
        st.error(f"无法连接到频率分析API: {str(e)}")
        st.info("请确保API服务(127.0.0.1:8888)正在运行")
        
        # 显示连接诊断信息
        with st.expander("🔍 连接诊断信息"):
            st.write("**API端点**: http://127.0.0.1:8888/api/v1/analysis/frequency")
            st.write("**错误类型**: 连接错误")
            st.write("**可能原因**:")
            st.write("- API服务未启动")
            st.write("- 频率分析模块未加载")
            st.write("- 数据库连接问题")
            st.write("**解决建议**:")
            st.write("- 检查API服务状态")
            st.write("- 确认数据库包含足够的历史数据")
            st.write("- 重启API服务")
            
    except Exception as e:
        st.error(f"频率分析出现错误: {str(e)}")
        
        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(str(e))

if __name__ == "__main__":
    show_frequency_analysis()
